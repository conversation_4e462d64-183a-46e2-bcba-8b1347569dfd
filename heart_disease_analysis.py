import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, roc_curve
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

def load_and_explore_data(file_path):
    """Load and explore the dataset"""
    print("=" * 50)
    print("LOADING AND EXPLORING DATA")
    print("=" * 50)
    
    # Load the data
    df = pd.read_csv(file_path)
    
    # Remove the first unnamed column (row index)
    if df.columns[0] == '' or 'Unnamed' in df.columns[0]:
        df = df.drop(df.columns[0], axis=1)
    
    print(f"Dataset shape: {df.shape}")
    print(f"\nColumn names: {list(df.columns)}")
    print(f"\nFirst few rows:")
    print(df.head())
    
    print(f"\nData types:")
    print(df.dtypes)
    
    print(f"\nMissing values:")
    print(df.isnull().sum())
    
    print(f"\nTarget variable distribution:")
    print(df['AHD'].value_counts())
    
    return df

def preprocess_data(df):
    """Preprocess the data"""
    print("\n" + "=" * 50)
    print("PREPROCESSING DATA")
    print("=" * 50)

    # Create a copy for preprocessing
    df_processed = df.copy()

    # Handle missing values
    print("Handling missing values...")
    print(f"Missing values before handling:")
    missing_before = df_processed.isnull().sum()
    print(missing_before[missing_before > 0])

    # For numerical columns with missing values, use median imputation
    numerical_cols_with_missing = ['Ca']  # Based on the data exploration
    for col in numerical_cols_with_missing:
        if col in df_processed.columns and df_processed[col].isnull().sum() > 0:
            median_val = df_processed[col].median()
            df_processed[col].fillna(median_val, inplace=True)
            print(f"  Filled {col} missing values with median: {median_val}")

    # For categorical columns with missing values, use mode imputation
    categorical_cols_with_missing = ['Thal']  # Based on the data exploration
    for col in categorical_cols_with_missing:
        if col in df_processed.columns and df_processed[col].isnull().sum() > 0:
            mode_val = df_processed[col].mode()[0]
            df_processed[col].fillna(mode_val, inplace=True)
            print(f"  Filled {col} missing values with mode: {mode_val}")

    print(f"Missing values after handling:")
    missing_after = df_processed.isnull().sum()
    print(missing_after[missing_after > 0] if missing_after.sum() > 0 else "No missing values remaining")

    # Handle categorical variables
    categorical_columns = ['ChestPain', 'RestECG', 'Slope', 'Thal']

    print("\nEncoding categorical variables...")
    label_encoders = {}

    for col in categorical_columns:
        if col in df_processed.columns:
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col].astype(str))
            label_encoders[col] = le
            print(f"  {col}: {dict(zip(le.classes_, le.transform(le.classes_)))}")

    # Encode target variable
    target_encoder = LabelEncoder()
    df_processed['AHD'] = target_encoder.fit_transform(df_processed['AHD'])
    print(f"  Target (AHD): {dict(zip(target_encoder.classes_, target_encoder.transform(target_encoder.classes_)))}")

    # Separate features and target
    X = df_processed.drop('AHD', axis=1)
    y = df_processed['AHD']

    print(f"\nFeatures shape: {X.shape}")
    print(f"Target shape: {y.shape}")
    print(f"Feature columns: {list(X.columns)}")

    return X, y, label_encoders, target_encoder

def split_and_scale_data(X, y, test_size=0.2, random_state=42):
    """Split and scale the data"""
    print("\n" + "=" * 50)
    print("SPLITTING AND SCALING DATA")
    print("=" * 50)
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    print(f"Training set size: {X_train.shape[0]} samples")
    print(f"Test set size: {X_test.shape[0]} samples")
    print(f"Training set target distribution: {np.bincount(y_train)}")
    print(f"Test set target distribution: {np.bincount(y_test)}")
    
    # Scale the features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print("Data scaling completed.")
    
    return X_train_scaled, X_test_scaled, y_train, y_test, scaler

def train_logistic_regression(X_train, y_train):
    """Train logistic regression model"""
    print("\n" + "=" * 50)
    print("TRAINING LOGISTIC REGRESSION MODEL")
    print("=" * 50)
    
    # Create and train the model
    model = LogisticRegression(random_state=42, max_iter=1000)
    model.fit(X_train, y_train)
    
    print("Logistic Regression model trained successfully!")
    print(f"Model coefficients shape: {model.coef_.shape}")
    print(f"Model intercept: {model.intercept_[0]:.4f}")
    
    return model

def evaluate_model(model, X_train, X_test, y_train, y_test, feature_names):
    """Evaluate the model performance"""
    print("\n" + "=" * 50)
    print("MODEL EVALUATION")
    print("=" * 50)
    
    # Make predictions
    y_train_pred = model.predict(X_train)
    y_test_pred = model.predict(X_test)
    y_test_proba = model.predict_proba(X_test)[:, 1]
    
    # Calculate metrics
    train_accuracy = accuracy_score(y_train, y_train_pred)
    test_accuracy = accuracy_score(y_test, y_test_pred)
    auc_score = roc_auc_score(y_test, y_test_proba)
    
    print(f"Training Accuracy: {train_accuracy:.4f}")
    print(f"Test Accuracy: {test_accuracy:.4f}")
    print(f"AUC Score: {auc_score:.4f}")
    
    print(f"\nClassification Report:")
    print(classification_report(y_test, y_test_pred))
    
    print(f"\nConfusion Matrix:")
    cm = confusion_matrix(y_test, y_test_pred)
    print(cm)
    
    # Feature importance
    print(f"\nFeature Importance (Coefficients):")
    feature_importance = pd.DataFrame({
        'Feature': feature_names,
        'Coefficient': model.coef_[0],
        'Abs_Coefficient': np.abs(model.coef_[0])
    }).sort_values('Abs_Coefficient', ascending=False)
    
    print(feature_importance)
    
    return {
        'train_accuracy': train_accuracy,
        'test_accuracy': test_accuracy,
        'auc_score': auc_score,
        'confusion_matrix': cm,
        'feature_importance': feature_importance
    }

def create_visualizations(results, y_test, y_test_proba):
    """Create visualizations"""
    print("\n" + "=" * 50)
    print("CREATING VISUALIZATIONS")
    print("=" * 50)
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Confusion Matrix
    sns.heatmap(results['confusion_matrix'], annot=True, fmt='d', cmap='Blues', ax=axes[0,0])
    axes[0,0].set_title('Confusion Matrix')
    axes[0,0].set_xlabel('Predicted')
    axes[0,0].set_ylabel('Actual')
    
    # Feature Importance
    top_features = results['feature_importance'].head(10)
    axes[0,1].barh(top_features['Feature'], top_features['Abs_Coefficient'])
    axes[0,1].set_title('Top 10 Feature Importance (Absolute Coefficients)')
    axes[0,1].set_xlabel('Absolute Coefficient Value')
    
    # ROC Curve
    fpr, tpr, _ = roc_curve(y_test, y_test_proba)
    axes[1,0].plot(fpr, tpr, label=f'ROC Curve (AUC = {results["auc_score"]:.3f})')
    axes[1,0].plot([0, 1], [0, 1], 'k--', label='Random')
    axes[1,0].set_xlabel('False Positive Rate')
    axes[1,0].set_ylabel('True Positive Rate')
    axes[1,0].set_title('ROC Curve')
    axes[1,0].legend()
    axes[1,0].grid(True)
    
    # Prediction Distribution
    axes[1,1].hist(y_test_proba, bins=20, alpha=0.7, edgecolor='black')
    axes[1,1].set_xlabel('Predicted Probability')
    axes[1,1].set_ylabel('Frequency')
    axes[1,1].set_title('Distribution of Predicted Probabilities')
    axes[1,1].grid(True)
    
    plt.tight_layout()
    plt.savefig('heart_disease_analysis_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Visualizations saved as 'heart_disease_analysis_results.png'")

def main():
    """Main function to run the complete analysis"""
    print("HEART DISEASE PREDICTION USING LOGISTIC REGRESSION")
    print("=" * 60)
    
    # Load and explore data
    df = load_and_explore_data('Heart.csv')
    
    # Preprocess data
    X, y, label_encoders, target_encoder = preprocess_data(df)
    
    # Split and scale data
    X_train, X_test, y_train, y_test, scaler = split_and_scale_data(X, y)
    
    # Train model
    model = train_logistic_regression(X_train, y_train)
    
    # Evaluate model
    results = evaluate_model(model, X_train, X_test, y_train, y_test, X.columns)
    
    # Create visualizations
    y_test_proba = model.predict_proba(X_test)[:, 1]
    create_visualizations(results, y_test, y_test_proba)
    
    print("\n" + "=" * 60)
    print("ANALYSIS COMPLETED SUCCESSFULLY!")
    print("=" * 60)

if __name__ == "__main__":
    main()
