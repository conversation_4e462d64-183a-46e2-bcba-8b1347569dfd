import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder, PolynomialFeatures
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.feature_selection import SelectKBest, f_classif, RFE
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

def load_and_explore_data(file_path):
    """Load and explore the dataset"""
    print("=" * 60)
    print("IMPROVED HEART DISEASE PREDICTION ANALYSIS")
    print("=" * 60)
    
    df = pd.read_csv(file_path)
    if df.columns[0] == '' or 'Unnamed' in df.columns[0]:
        df = df.drop(df.columns[0], axis=1)
    
    print(f"Dataset shape: {df.shape}")
    print(f"Target distribution: {df['AHD'].value_counts().to_dict()}")
    return df

def advanced_preprocessing(df):
    """Advanced preprocessing with feature engineering"""
    print("\n" + "=" * 50)
    print("ADVANCED PREPROCESSING & FEATURE ENGINEERING")
    print("=" * 50)
    
    df_processed = df.copy()
    
    # Handle missing values
    df_processed['Ca'].fillna(df_processed['Ca'].median(), inplace=True)
    df_processed['Thal'].fillna(df_processed['Thal'].mode()[0], inplace=True)
    
    # Feature Engineering - Create new features
    print("Creating new features...")
    
    # Age groups
    df_processed['Age_Group'] = pd.cut(df_processed['Age'], 
                                     bins=[0, 45, 55, 65, 100], 
                                     labels=['Young', 'Middle', 'Senior', 'Elderly'])
    
    # Cholesterol risk levels
    df_processed['Chol_Risk'] = pd.cut(df_processed['Chol'], 
                                     bins=[0, 200, 240, 1000], 
                                     labels=['Normal', 'Borderline', 'High'])
    
    # Blood pressure categories
    df_processed['BP_Category'] = pd.cut(df_processed['RestBP'], 
                                       bins=[0, 120, 140, 1000], 
                                       labels=['Normal', 'Elevated', 'High'])
    
    # Heart rate zones
    df_processed['HR_Zone'] = pd.cut(df_processed['MaxHR'], 
                                   bins=[0, 100, 150, 200, 1000], 
                                   labels=['Low', 'Moderate', 'High', 'Maximum'])
    
    # Interaction features
    df_processed['Age_Chol_Interaction'] = df_processed['Age'] * df_processed['Chol'] / 1000
    df_processed['Sex_ChestPain_Interaction'] = df_processed['Sex'].astype(str) + '_' + df_processed['ChestPain'].astype(str)
    
    # Encode all categorical variables
    categorical_columns = ['ChestPain', 'RestECG', 'Slope', 'Thal', 'Age_Group', 
                          'Chol_Risk', 'BP_Category', 'HR_Zone', 'Sex_ChestPain_Interaction']
    
    label_encoders = {}
    for col in categorical_columns:
        if col in df_processed.columns:
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col].astype(str))
            label_encoders[col] = le
    
    # Encode target
    target_encoder = LabelEncoder()
    df_processed['AHD'] = target_encoder.fit_transform(df_processed['AHD'])
    
    X = df_processed.drop('AHD', axis=1)
    y = df_processed['AHD']
    
    print(f"Features after engineering: {X.shape[1]}")
    print(f"New features created: {X.shape[1] - 13}")
    
    return X, y, label_encoders, target_encoder

def feature_selection(X, y):
    """Advanced feature selection"""
    print("\n" + "=" * 50)
    print("FEATURE SELECTION")
    print("=" * 50)
    
    # Statistical feature selection
    selector = SelectKBest(score_func=f_classif, k=15)
    X_selected = selector.fit_transform(X, y)
    selected_features = X.columns[selector.get_support()].tolist()
    
    print(f"Selected {len(selected_features)} best features:")
    feature_scores = pd.DataFrame({
        'Feature': X.columns,
        'Score': selector.scores_,
        'Selected': selector.get_support()
    }).sort_values('Score', ascending=False)
    
    print(feature_scores.head(10))
    
    return X_selected, selected_features, selector

def train_multiple_models(X_train, X_test, y_train, y_test):
    """Train and compare multiple models"""
    print("\n" + "=" * 50)
    print("TRAINING MULTIPLE MODELS")
    print("=" * 50)
    
    models = {
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'Random Forest': RandomForestClassifier(random_state=42, n_estimators=100),
        'Gradient Boosting': GradientBoostingClassifier(random_state=42),
        'SVM': SVC(random_state=42, probability=True)
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"\nTraining {name}...")
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
        
        # Train and evaluate
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        y_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
        
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_proba) if y_proba is not None else None
        
        results[name] = {
            'model': model,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'test_accuracy': accuracy,
            'auc': auc,
            'predictions': y_pred,
            'probabilities': y_proba
        }
        
        print(f"  CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        print(f"  Test Accuracy: {accuracy:.4f}")
        if auc:
            print(f"  AUC: {auc:.4f}")
    
    return results

def hyperparameter_tuning(X_train, y_train, best_model_name, models_results):
    """Hyperparameter tuning for the best model"""
    print("\n" + "=" * 50)
    print(f"HYPERPARAMETER TUNING - {best_model_name}")
    print("=" * 50)
    
    if best_model_name == 'Logistic Regression':
        param_grid = {
            'C': [0.01, 0.1, 1, 10, 100],
            'penalty': ['l1', 'l2'],
            'solver': ['liblinear', 'saga']
        }
        model = LogisticRegression(random_state=42, max_iter=1000)
        
    elif best_model_name == 'Random Forest':
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [None, 10, 20, 30],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        model = RandomForestClassifier(random_state=42)
        
    elif best_model_name == 'Gradient Boosting':
        param_grid = {
            'n_estimators': [50, 100, 200],
            'learning_rate': [0.01, 0.1, 0.2],
            'max_depth': [3, 5, 7],
            'subsample': [0.8, 0.9, 1.0]
        }
        model = GradientBoostingClassifier(random_state=42)
        
    else:  # SVM
        param_grid = {
            'C': [0.1, 1, 10, 100],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
            'kernel': ['rbf', 'poly', 'sigmoid']
        }
        model = SVC(random_state=42, probability=True)
    
    # Grid search with cross-validation
    grid_search = GridSearchCV(
        model, param_grid, cv=5, scoring='accuracy', n_jobs=-1, verbose=1
    )
    
    grid_search.fit(X_train, y_train)
    
    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Best CV score: {grid_search.best_score_:.4f}")
    
    return grid_search.best_estimator_

def evaluate_final_model(model, X_train, X_test, y_train, y_test, feature_names):
    """Comprehensive evaluation of the final model"""
    print("\n" + "=" * 50)
    print("FINAL MODEL EVALUATION")
    print("=" * 50)
    
    # Predictions
    y_train_pred = model.predict(X_train)
    y_test_pred = model.predict(X_test)
    y_test_proba = model.predict_proba(X_test)[:, 1]
    
    # Metrics
    train_acc = accuracy_score(y_train, y_train_pred)
    test_acc = accuracy_score(y_test, y_test_pred)
    auc = roc_auc_score(y_test, y_test_proba)
    
    print(f"Training Accuracy: {train_acc:.4f}")
    print(f"Test Accuracy: {test_acc:.4f}")
    print(f"AUC Score: {auc:.4f}")
    print(f"Improvement over baseline: {(test_acc - 0.9016) * 100:.2f} percentage points")
    
    print(f"\nClassification Report:")
    print(classification_report(y_test, y_test_pred))
    
    cm = confusion_matrix(y_test, y_test_pred)
    print(f"\nConfusion Matrix:")
    print(cm)
    
    # Feature importance (if available)
    if hasattr(model, 'feature_importances_'):
        importance_df = pd.DataFrame({
            'Feature': feature_names,
            'Importance': model.feature_importances_
        }).sort_values('Importance', ascending=False)
        print(f"\nTop 10 Feature Importances:")
        print(importance_df.head(10))
    elif hasattr(model, 'coef_'):
        importance_df = pd.DataFrame({
            'Feature': feature_names,
            'Coefficient': model.coef_[0],
            'Abs_Coefficient': np.abs(model.coef_[0])
        }).sort_values('Abs_Coefficient', ascending=False)
        print(f"\nTop 10 Feature Coefficients:")
        print(importance_df.head(10))
    
    return {
        'train_accuracy': train_acc,
        'test_accuracy': test_acc,
        'auc_score': auc,
        'confusion_matrix': cm,
        'model': model
    }

def create_advanced_visualizations(results, y_test, y_test_proba, model_comparison):
    """Create comprehensive visualizations"""
    print("\n" + "=" * 50)
    print("CREATING ADVANCED VISUALIZATIONS")
    print("=" * 50)
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. Model Comparison
    model_names = list(model_comparison.keys())
    accuracies = [model_comparison[name]['test_accuracy'] for name in model_names]
    
    axes[0,0].bar(model_names, accuracies, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
    axes[0,0].set_title('Model Comparison - Test Accuracy')
    axes[0,0].set_ylabel('Accuracy')
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Confusion Matrix
    sns.heatmap(results['confusion_matrix'], annot=True, fmt='d', cmap='Blues', ax=axes[0,1])
    axes[0,1].set_title('Final Model - Confusion Matrix')
    axes[0,1].set_xlabel('Predicted')
    axes[0,1].set_ylabel('Actual')
    
    # 3. ROC Curve
    fpr, tpr, _ = roc_curve(y_test, y_test_proba)
    axes[0,2].plot(fpr, tpr, label=f'ROC Curve (AUC = {results["auc_score"]:.3f})')
    axes[0,2].plot([0, 1], [0, 1], 'k--', label='Random')
    axes[0,2].set_xlabel('False Positive Rate')
    axes[0,2].set_ylabel('True Positive Rate')
    axes[0,2].set_title('ROC Curve')
    axes[0,2].legend()
    axes[0,2].grid(True)
    
    # 4. Cross-validation scores
    cv_means = [model_comparison[name]['cv_mean'] for name in model_names]
    cv_stds = [model_comparison[name]['cv_std'] for name in model_names]
    
    axes[1,0].bar(model_names, cv_means, yerr=cv_stds, capsize=5, 
                  color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
    axes[1,0].set_title('Cross-Validation Scores')
    axes[1,0].set_ylabel('CV Accuracy')
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # 5. Prediction Distribution
    axes[1,1].hist(y_test_proba, bins=20, alpha=0.7, edgecolor='black')
    axes[1,1].set_xlabel('Predicted Probability')
    axes[1,1].set_ylabel('Frequency')
    axes[1,1].set_title('Distribution of Predicted Probabilities')
    axes[1,1].grid(True)
    
    # 6. Performance Metrics Comparison
    metrics = ['Accuracy', 'AUC']
    baseline = [0.9016, 0.9524]  # Previous results
    improved = [results['test_accuracy'], results['auc_score']]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    axes[1,2].bar(x - width/2, baseline, width, label='Baseline', color='lightcoral')
    axes[1,2].bar(x + width/2, improved, width, label='Improved', color='lightgreen')
    axes[1,2].set_xlabel('Metrics')
    axes[1,2].set_ylabel('Score')
    axes[1,2].set_title('Baseline vs Improved Model')
    axes[1,2].set_xticks(x)
    axes[1,2].set_xticklabels(metrics)
    axes[1,2].legend()
    axes[1,2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('improved_heart_analysis_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Advanced visualizations saved as 'improved_heart_analysis_results.png'")

def main():
    """Main function"""
    # Load data
    df = load_and_explore_data('Heart.csv')
    
    # Advanced preprocessing
    X, y, label_encoders, target_encoder = advanced_preprocessing(df)
    
    # Feature selection
    X_selected, selected_features, selector = feature_selection(X, y)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X_selected, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Train multiple models
    model_results = train_multiple_models(X_train_scaled, X_test_scaled, y_train, y_test)
    
    # Find best model
    best_model_name = max(model_results.keys(), key=lambda x: model_results[x]['test_accuracy'])
    print(f"\nBest performing model: {best_model_name}")
    
    # Hyperparameter tuning
    best_model = hyperparameter_tuning(X_train_scaled, y_train, best_model_name, model_results)
    
    # Final evaluation
    results = evaluate_final_model(best_model, X_train_scaled, X_test_scaled, 
                                 y_train, y_test, selected_features)
    
    # Visualizations
    y_test_proba = best_model.predict_proba(X_test_scaled)[:, 1]
    create_advanced_visualizations(results, y_test, y_test_proba, model_results)
    
    print("\n" + "=" * 60)
    print("IMPROVED ANALYSIS COMPLETED!")
    print(f"Final Test Accuracy: {results['test_accuracy']:.4f}")
    print(f"Final AUC Score: {results['auc_score']:.4f}")
    print("=" * 60)

if __name__ == "__main__":
    main()
