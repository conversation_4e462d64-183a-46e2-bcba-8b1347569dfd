{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Heart Disease Prediction with Polynomial Features\n", "\n", "This notebook applies polynomial features of degree 2 and 3 to improve the accuracy of heart disease prediction using logistic regression.\n", "\n", "## Approach:\n", "1. Load and preprocess data\n", "2. Apply polynomial features (degree 2 and 3)\n", "3. Train logistic regression models\n", "4. Compare accuracies\n", "5. Visualize results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder, PolynomialFeatures\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, roc_curve\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load and Preprocess Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (303, 14)\n", "Target distribution: AHD\n", "No     164\n", "Yes    139\n", "Name: count, dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Sex</th>\n", "      <th>ChestPain</th>\n", "      <th>RestBP</th>\n", "      <th>Chol</th>\n", "      <th>Fbs</th>\n", "      <th>RestECG</th>\n", "      <th>MaxHR</th>\n", "      <th>ExAng</th>\n", "      <th>Oldpeak</th>\n", "      <th>Slope</th>\n", "      <th>Ca</th>\n", "      <th>Thal</th>\n", "      <th>AHD</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>63</td>\n", "      <td>1</td>\n", "      <td>typical</td>\n", "      <td>145</td>\n", "      <td>233</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>150</td>\n", "      <td>0</td>\n", "      <td>2.3</td>\n", "      <td>3</td>\n", "      <td>0.0</td>\n", "      <td>fixed</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67</td>\n", "      <td>1</td>\n", "      <td>asymptomatic</td>\n", "      <td>160</td>\n", "      <td>286</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>108</td>\n", "      <td>1</td>\n", "      <td>1.5</td>\n", "      <td>2</td>\n", "      <td>3.0</td>\n", "      <td>normal</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67</td>\n", "      <td>1</td>\n", "      <td>asymptomatic</td>\n", "      <td>120</td>\n", "      <td>229</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>129</td>\n", "      <td>1</td>\n", "      <td>2.6</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "      <td>reversable</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>37</td>\n", "      <td>1</td>\n", "      <td>nonanginal</td>\n", "      <td>130</td>\n", "      <td>250</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>187</td>\n", "      <td>0</td>\n", "      <td>3.5</td>\n", "      <td>3</td>\n", "      <td>0.0</td>\n", "      <td>normal</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>41</td>\n", "      <td>0</td>\n", "      <td>nontypical</td>\n", "      <td>130</td>\n", "      <td>204</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>172</td>\n", "      <td>0</td>\n", "      <td>1.4</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>normal</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Age  Sex     ChestPain  RestBP  Chol  Fbs  RestECG  MaxHR  ExAng  Oldpeak  \\\n", "0   63    1       typical     145   233    1        2    150      0      2.3   \n", "1   67    1  asymptomatic     160   286    0        2    108      1      1.5   \n", "2   67    1  asymptomatic     120   229    0        2    129      1      2.6   \n", "3   37    1    nonanginal     130   250    0        0    187      0      3.5   \n", "4   41    0    nontypical     130   204    0        2    172      0      1.4   \n", "\n", "   Slope   Ca        Thal  AHD  \n", "0      3  0.0       fixed   No  \n", "1      2  3.0      normal  Yes  \n", "2      2  2.0  reversable  Yes  \n", "3      3  0.0      normal   No  \n", "4      1  0.0      normal   No  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the dataset\n", "df = pd.read_csv('Heart.csv')\n", "\n", "# Remove the first unnamed column if it exists\n", "if df.columns[0] == '' or 'Unnamed' in df.columns[0]:\n", "    df = df.drop(df.columns[0], axis=1)\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Target distribution: {df['AHD'].value_counts()}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values:\n", "Ca      4\n", "Thal    2\n", "dtype: int64\n"]}], "source": ["# Check for missing values\n", "print(\"Missing values:\")\n", "missing_values = df.isnull().sum()\n", "print(missing_values[missing_values > 0])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values handled.\n", "Remaining missing values: 0\n"]}], "source": ["# Preprocess the data\n", "df_processed = df.copy()\n", "\n", "# Handle missing values\n", "df_processed['Ca'].fillna(df_processed['Ca'].median(), inplace=True)\n", "df_processed['Thal'].fillna(df_processed['Thal'].mode()[0], inplace=True)\n", "\n", "print(\"Missing values handled.\")\n", "print(f\"Remaining missing values: {df_processed.isnull().sum().sum()}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Encoding categorical variables:\n", "  ChestPain: 4 categories\n", "  RestECG: 3 categories\n", "  Slope: 3 categories\n", "  Thal: 3 categories\n", "Target encoded: {'No': 0, 'Yes': 1}\n"]}], "source": ["# Encode categorical variables\n", "categorical_columns = ['ChestPain', 'RestECG', 'Slope', 'Thal']\n", "label_encoders = {}\n", "\n", "print(\"Encoding categorical variables:\")\n", "for col in categorical_columns:\n", "    if col in df_processed.columns:\n", "        le = LabelEncoder()\n", "        df_processed[col] = le.fit_transform(df_processed[col].astype(str))\n", "        label_encoders[col] = le\n", "        print(f\"  {col}: {len(le.classes_)} categories\")\n", "\n", "# Encode target variable\n", "target_encoder = LabelEncoder()\n", "df_processed['AHD'] = target_encoder.fit_transform(df_processed['AHD'])\n", "print(f\"Target encoded: {dict(zip(target_encoder.classes_, target_encoder.transform(target_encoder.classes_)))}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original features shape: (303, 13)\n", "Feature columns: ['Age', 'Sex', 'Chest<PERSON>ain', 'RestBP', 'Chol', 'Fbs', 'RestECG', 'MaxHR', 'ExAng', 'Oldpeak', 'Slope', 'Ca', 'Thal']\n"]}], "source": ["# Separate features and target\n", "X = df_processed.drop('AHD', axis=1)\n", "y = df_processed['AHD']\n", "\n", "print(f\"Original features shape: {X.shape}\")\n", "print(f\"Feature columns: {list(X.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Split Data"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training set: 242 samples\n", "Test set: 61 samples\n", "Training target distribution: [131 111]\n", "Test target distribution: [33 28]\n"]}], "source": ["# Split the data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "print(f\"Training set: {X_train.shape[0]} samples\")\n", "print(f\"Test set: {X_test.shape[0]} samples\")\n", "print(f\"Training target distribution: {np.bincount(y_train)}\")\n", "print(f\"Test target distribution: {np.bincount(y_test)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Baseline Model (No Polynomial Features)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "BASELINE MODEL (No Polynomial Features)\n", "==================================================\n", "Baseline Training Accuracy: 0.8388\n", "Baseline Test Accuracy: 0.9016\n", "Baseline AUC Score: 0.9524\n", "Number of features: 13\n"]}], "source": ["# Baseline model with standard scaling\n", "print(\"=\" * 50)\n", "print(\"BASELINE MODEL (No Polynomial Features)\")\n", "print(\"=\" * 50)\n", "\n", "# Scale features\n", "scaler_baseline = StandardScaler()\n", "X_train_scaled = scaler_baseline.fit_transform(X_train)\n", "X_test_scaled = scaler_baseline.transform(X_test)\n", "\n", "# Train baseline model\n", "baseline_model = LogisticRegression(random_state=42, max_iter=1000)\n", "baseline_model.fit(X_train_scaled, y_train)\n", "\n", "# Evaluate baseline\n", "baseline_train_pred = baseline_model.predict(X_train_scaled)\n", "baseline_test_pred = baseline_model.predict(X_test_scaled)\n", "baseline_test_proba = baseline_model.predict_proba(X_test_scaled)[:, 1]\n", "\n", "baseline_train_acc = accuracy_score(y_train, baseline_train_pred)\n", "baseline_test_acc = accuracy_score(y_test, baseline_test_pred)\n", "baseline_auc = roc_auc_score(y_test, baseline_test_proba)\n", "\n", "print(f\"Baseline Training Accuracy: {baseline_train_acc:.4f}\")\n", "print(f\"Baseline Test Accuracy: {baseline_test_acc:.4f}\")\n", "print(f\"Baseline AUC Score: {baseline_auc:.4f}\")\n", "print(f\"Number of features: {X_train_scaled.shape[1]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Polynomial Features - Degree 2"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "POLYNOMIAL FEATURES - DEGREE 2\n", "==================================================\n", "Original features: 13\n", "Polynomial degree 2 features: 104\n", "Feature expansion ratio: 8.0x\n", "\n", "Polynomial Degree 2 Results:\n", "Training Accuracy: 0.8802\n", "Test Accuracy: 0.8852\n", "AUC Score: 0.9448\n", "Improvement over baseline: -1.64 percentage points\n"]}], "source": ["print(\"=\" * 50)\n", "print(\"POLYNOMIAL FEATURES - DEGREE 2\")\n", "print(\"=\" * 50)\n", "\n", "# Create polynomial features of degree 2\n", "poly_2 = PolynomialFeatures(degree=2, include_bias=False)\n", "X_train_poly2 = poly_2.fit_transform(X_train)\n", "X_test_poly2 = poly_2.transform(X_test)\n", "\n", "print(f\"Original features: {X_train.shape[1]}\")\n", "print(f\"Polynomial degree 2 features: {X_train_poly2.shape[1]}\")\n", "print(f\"Feature expansion ratio: {X_train_poly2.shape[1] / X_train.shape[1]:.1f}x\")\n", "\n", "# Scale polynomial features\n", "scaler_poly2 = StandardScaler()\n", "X_train_poly2_scaled = scaler_poly2.fit_transform(X_train_poly2)\n", "X_test_poly2_scaled = scaler_poly2.transform(X_test_poly2)\n", "\n", "# Train model with polynomial features degree 2\n", "model_poly2 = LogisticRegression(random_state=42, max_iter=2000, C=0.1)  # Regularization to prevent overfitting\n", "model_poly2.fit(X_train_poly2_scaled, y_train)\n", "\n", "# Evaluate polynomial degree 2 model\n", "poly2_train_pred = model_poly2.predict(X_train_poly2_scaled)\n", "poly2_test_pred = model_poly2.predict(X_test_poly2_scaled)\n", "poly2_test_proba = model_poly2.predict_proba(X_test_poly2_scaled)[:, 1]\n", "\n", "poly2_train_acc = accuracy_score(y_train, poly2_train_pred)\n", "poly2_test_acc = accuracy_score(y_test, poly2_test_pred)\n", "poly2_auc = roc_auc_score(y_test, poly2_test_proba)\n", "\n", "print(f\"\\nPolynomial Degree 2 Results:\")\n", "print(f\"Training Accuracy: {poly2_train_acc:.4f}\")\n", "print(f\"Test Accuracy: {poly2_test_acc:.4f}\")\n", "print(f\"AUC Score: {poly2_auc:.4f}\")\n", "print(f\"Improvement over baseline: {(poly2_test_acc - baseline_test_acc)*100:.2f} percentage points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Polynomial Features - Degree 3"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "POLYNOMIAL FEATURES - DEGREE 3\n", "==================================================\n", "Original features: 13\n", "Polynomial degree 3 features: 559\n", "Feature expansion ratio: 43.0x\n", "\n", "Polynomial Degree 3 Results:\n", "Training Accuracy: 0.8760\n", "Test Accuracy: 0.8852\n", "AUC Score: 0.9383\n", "Improvement over baseline: -1.64 percentage points\n"]}], "source": ["print(\"=\" * 50)\n", "print(\"POLYNOMIAL FEATURES - DEGREE 3\")\n", "print(\"=\" * 50)\n", "\n", "# Create polynomial features of degree 3\n", "poly_3 = PolynomialFeatures(degree=3, include_bias=False)\n", "X_train_poly3 = poly_3.fit_transform(X_train)\n", "X_test_poly3 = poly_3.transform(X_test)\n", "\n", "print(f\"Original features: {X_train.shape[1]}\")\n", "print(f\"Polynomial degree 3 features: {X_train_poly3.shape[1]}\")\n", "print(f\"Feature expansion ratio: {X_train_poly3.shape[1] / X_train.shape[1]:.1f}x\")\n", "\n", "# Scale polynomial features\n", "scaler_poly3 = StandardScaler()\n", "X_train_poly3_scaled = scaler_poly3.fit_transform(X_train_poly3)\n", "X_test_poly3_scaled = scaler_poly3.transform(X_test_poly3)\n", "\n", "# Train model with polynomial features degree 3\n", "model_poly3 = LogisticRegression(random_state=42, max_iter=3000, C=0.01)  # More regularization for degree 3\n", "model_poly3.fit(X_train_poly3_scaled, y_train)\n", "\n", "# Evaluate polynomial degree 3 model\n", "poly3_train_pred = model_poly3.predict(X_train_poly3_scaled)\n", "poly3_test_pred = model_poly3.predict(X_test_poly3_scaled)\n", "poly3_test_proba = model_poly3.predict_proba(X_test_poly3_scaled)[:, 1]\n", "\n", "poly3_train_acc = accuracy_score(y_train, poly3_train_pred)\n", "poly3_test_acc = accuracy_score(y_test, poly3_test_pred)\n", "poly3_auc = roc_auc_score(y_test, poly3_test_proba)\n", "\n", "print(f\"\\nPolynomial Degree 3 Results:\")\n", "print(f\"Training Accuracy: {poly3_train_acc:.4f}\")\n", "print(f\"Test Accuracy: {poly3_test_acc:.4f}\")\n", "print(f\"AUC Score: {poly3_auc:.4f}\")\n", "print(f\"Improvement over baseline: {(poly3_test_acc - baseline_test_acc)*100:.2f} percentage points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cross-Validation Analysis"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "CROSS-VALIDATION ANALYSIS\n", "==================================================\n", "Baseline CV Accuracy: 0.8180 (+/- 0.0722)\n", "Polynomial Degree 2 CV Accuracy: 0.8345 (+/- 0.0653)\n", "Polynomial Degree 3 CV Accuracy: 0.8262 (+/- 0.0738)\n"]}], "source": ["print(\"=\" * 50)\n", "print(\"CROSS-VALIDATION ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Cross-validation for all models\n", "cv_baseline = cross_val_score(baseline_model, X_train_scaled, y_train, cv=5, scoring='accuracy')\n", "cv_poly2 = cross_val_score(model_poly2, X_train_poly2_scaled, y_train, cv=5, scoring='accuracy')\n", "cv_poly3 = cross_val_score(model_poly3, X_train_poly3_scaled, y_train, cv=5, scoring='accuracy')\n", "\n", "print(f\"Baseline CV Accuracy: {cv_baseline.mean():.4f} (+/- {cv_baseline.std() * 2:.4f})\")\n", "print(f\"Polynomial Degree 2 CV Accuracy: {cv_poly2.mean():.4f} (+/- {cv_poly2.std() * 2:.4f})\")\n", "print(f\"Polynomial Degree 3 CV Accuracy: {cv_poly3.mean():.4f} (+/- {cv_poly3.std() * 2:.4f})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results Summary"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["======================================================================\n", "COMPREHENSIVE RESULTS SUMMARY\n", "======================================================================\n", "                 Model  Features  Train_Accuracy  Test_Accuracy  AUC_Score  \\\n", "0             Baseline        13          0.8388         0.9016     0.9524   \n", "1  Polynomial Degree 2       104          0.8802         0.8852     0.9448   \n", "2  Polynomial Degree 3       559          0.8760         0.8852     0.9383   \n", "\n", "   CV_Mean  CV_Std  \n", "0   0.8180  0.0361  \n", "1   0.8345  0.0326  \n", "2   0.8262  0.0369  \n", "\n", "🏆 BEST MODEL: Baseline\n", "🎯 BEST TEST ACCURACY: 0.9016 (90.16%)\n", "📈 IMPROVEMENT: 0.00 percentage points\n"]}], "source": ["# Create results summary\n", "results_summary = pd.DataFrame({\n", "    'Model': ['Baseline', 'Polynomial Degree 2', 'Polynomial Degree 3'],\n", "    'Features': [X_train.shape[1], X_train_poly2.shape[1], X_train_poly3.shape[1]],\n", "    'Train_Accuracy': [baseline_train_acc, poly2_train_acc, poly3_train_acc],\n", "    'Test_Accuracy': [baseline_test_acc, poly2_test_acc, poly3_test_acc],\n", "    'AUC_Score': [baseline_auc, poly2_auc, poly3_auc],\n", "    'CV_Mean': [cv_baseline.mean(), cv_poly2.mean(), cv_poly3.mean()],\n", "    'CV_Std': [cv_baseline.std(), cv_poly2.std(), cv_poly3.std()]\n", "})\n", "\n", "print(\"=\" * 70)\n", "print(\"COMPREHENSIVE RESULTS SUMMARY\")\n", "print(\"=\" * 70)\n", "print(results_summary.round(4))\n", "\n", "# Find best model\n", "best_model_idx = results_summary['Test_Accuracy'].idxmax()\n", "best_model_name = results_summary.loc[best_model_idx, 'Model']\n", "best_accuracy = results_summary.loc[best_model_idx, 'Test_Accuracy']\n", "\n", "print(f\"\\n🏆 BEST MODEL: {best_model_name}\")\n", "print(f\"🎯 BEST TEST ACCURACY: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)\")\n", "print(f\"📈 IMPROVEMENT: {(best_accuracy - baseline_test_acc)*100:.2f} percentage points\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}