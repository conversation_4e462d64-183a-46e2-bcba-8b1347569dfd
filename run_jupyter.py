"""
<PERSON>ript to launch Jupyter Notebook for the heart disease analysis
"""

import subprocess
import sys
import os

def check_jupyter_installed():
    """Check if <PERSON><PERSON><PERSON> is installed"""
    try:
        subprocess.run(['jupyter', '--version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_jupyter():
    """Install Jupyter if not present"""
    print("<PERSON><PERSON><PERSON> not found. Installing...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'jupyter'], check=True)
        print("Jupyter installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("Failed to install <PERSON>pyter. Please install manually: pip install jupyter")
        return False

def launch_jupyter():
    """Launch Jupyter Notebook"""
    try:
        print("Launching Jupyter Notebook...")
        print("The notebook 'heart_disease_analysis.ipynb' will be available in the browser.")
        print("Press Ctrl+C to stop the <PERSON>pyter server when done.")
        subprocess.run(['jupyter', 'notebook', 'heart_disease_analysis.ipynb'])
    except KeyboardInterrupt:
        print("\nJupyter Notebook server stopped.")
    except subprocess.CalledProcessError as e:
        print(f"Error launching Jupyter: {e}")

def main():
    print("=" * 50)
    print("HEART DISEASE ANALYSIS - JUPYTER LAUNCHER")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('heart_disease_analysis.ipynb'):
        print("Error: heart_disease_analysis.ipynb not found in current directory!")
        return
    
    if not os.path.exists('Heart.csv'):
        print("Error: Heart.csv not found in current directory!")
        return
    
    # Check and install Jupyter if needed
    if not check_jupyter_installed():
        if not install_jupyter():
            return
    
    # Launch Jupyter
    launch_jupyter()

if __name__ == "__main__":
    main()
