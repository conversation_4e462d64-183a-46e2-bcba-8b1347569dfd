{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Heart Disease Prediction with Polynomial Features\n", "\n", "This notebook applies polynomial features of degree 2 and 3 to improve accuracy.\n", "\n", "## Objectives:\n", "- Compare baseline logistic regression with polynomial feature versions\n", "- Apply polynomial features of degree 2 and 3\n", "- Evaluate and compare model performance\n", "- Visualize results and improvements"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder, PolynomialFeatures\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, roc_curve\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and preprocess data\n", "df = pd.read_csv('Heart.csv')\n", "if df.columns[0] == '' or 'Unnamed' in df.columns[0]:\n", "    df = df.drop(df.columns[0], axis=1)\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Missing values: {df.isnull().sum().sum()}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle missing values and encode categorical variables\n", "df_processed = df.copy()\n", "df_processed['Ca'].fillna(df_processed['Ca'].median(), inplace=True)\n", "df_processed['Thal'].fillna(df_processed['Thal'].mode()[0], inplace=True)\n", "\n", "# Encode categorical variables\n", "categorical_columns = ['ChestPain', 'RestECG', 'Slope', 'Thal']\n", "for col in categorical_columns:\n", "    le = LabelEncoder()\n", "    df_processed[col] = le.fit_transform(df_processed[col].astype(str))\n", "\n", "# Encode target\n", "target_encoder = LabelEncoder()\n", "df_processed['AHD'] = target_encoder.fit_transform(df_processed['AHD'])\n", "\n", "X = df_processed.drop('AHD', axis=1)\n", "y = df_processed['AHD']\n", "\n", "print(f\"Preprocessed features shape: {X.shape}\")\n", "print(f\"Target distribution: {np.bincount(y)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "print(f\"Training set: {X_train.shape[0]} samples\")\n", "print(f\"Test set: {X_test.shape[0]} samples\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Baseline Model (No Polynomial Features)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=\" * 50)\n", "print(\"BASELINE MODEL\")\n", "print(\"=\" * 50)\n", "\n", "# Scale features\n", "scaler_baseline = StandardScaler()\n", "X_train_scaled = scaler_baseline.fit_transform(X_train)\n", "X_test_scaled = scaler_baseline.transform(X_test)\n", "\n", "# Train baseline model\n", "baseline_model = LogisticRegression(random_state=42, max_iter=1000)\n", "baseline_model.fit(X_train_scaled, y_train)\n", "\n", "# Evaluate\n", "baseline_train_pred = baseline_model.predict(X_train_scaled)\n", "baseline_test_pred = baseline_model.predict(X_test_scaled)\n", "baseline_test_proba = baseline_model.predict_proba(X_test_scaled)[:, 1]\n", "\n", "baseline_train_acc = accuracy_score(y_train, baseline_train_pred)\n", "baseline_test_acc = accuracy_score(y_test, baseline_test_pred)\n", "baseline_auc = roc_auc_score(y_test, baseline_test_proba)\n", "\n", "print(f\"Features: {X_train_scaled.shape[1]}\")\n", "print(f\"Training Accuracy: {baseline_train_acc:.4f}\")\n", "print(f\"Test Accuracy: {baseline_test_acc:.4f}\")\n", "print(f\"AUC Score: {baseline_auc:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Polynomial Features - Degree 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=\" * 50)\n", "print(\"POLYNOMIAL FEATURES - DEGREE 2\")\n", "print(\"=\" * 50)\n", "\n", "# Create polynomial features degree 2\n", "poly_2 = PolynomialFeatures(degree=2, include_bias=False)\n", "X_train_poly2 = poly_2.fit_transform(X_train)\n", "X_test_poly2 = poly_2.transform(X_test)\n", "\n", "print(f\"Original features: {X_train.shape[1]}\")\n", "print(f\"Polynomial degree 2 features: {X_train_poly2.shape[1]}\")\n", "print(f\"Expansion ratio: {X_train_poly2.shape[1] / X_train.shape[1]:.1f}x\")\n", "\n", "# Scale\n", "scaler_poly2 = StandardScaler()\n", "X_train_poly2_scaled = scaler_poly2.fit_transform(X_train_poly2)\n", "X_test_poly2_scaled = scaler_poly2.transform(X_test_poly2)\n", "\n", "# Train model with regularization\n", "model_poly2 = LogisticRegression(random_state=42, max_iter=2000, C=0.1)\n", "model_poly2.fit(X_train_poly2_scaled, y_train)\n", "\n", "# Evaluate\n", "poly2_train_pred = model_poly2.predict(X_train_poly2_scaled)\n", "poly2_test_pred = model_poly2.predict(X_test_poly2_scaled)\n", "poly2_test_proba = model_poly2.predict_proba(X_test_poly2_scaled)[:, 1]\n", "\n", "poly2_train_acc = accuracy_score(y_train, poly2_train_pred)\n", "poly2_test_acc = accuracy_score(y_test, poly2_test_pred)\n", "poly2_auc = roc_auc_score(y_test, poly2_test_proba)\n", "\n", "print(f\"Training Accuracy: {poly2_train_acc:.4f}\")\n", "print(f\"Test Accuracy: {poly2_test_acc:.4f}\")\n", "print(f\"AUC Score: {poly2_auc:.4f}\")\n", "print(f\"Improvement: {(poly2_test_acc - baseline_test_acc)*100:.2f} percentage points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Polynomial Features - Degree 3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=\" * 50)\n", "print(\"POLYNOMIAL FEATURES - DEGREE 3\")\n", "print(\"=\" * 50)\n", "\n", "# Create polynomial features degree 3\n", "poly_3 = PolynomialFeatures(degree=3, include_bias=False)\n", "X_train_poly3 = poly_3.fit_transform(X_train)\n", "X_test_poly3 = poly_3.transform(X_test)\n", "\n", "print(f\"Original features: {X_train.shape[1]}\")\n", "print(f\"Polynomial degree 3 features: {X_train_poly3.shape[1]}\")\n", "print(f\"Expansion ratio: {X_train_poly3.shape[1] / X_train.shape[1]:.1f}x\")\n", "\n", "# Scale\n", "scaler_poly3 = StandardScaler()\n", "X_train_poly3_scaled = scaler_poly3.fit_transform(X_train_poly3)\n", "X_test_poly3_scaled = scaler_poly3.transform(X_test_poly3)\n", "\n", "# Train model with more regularization\n", "model_poly3 = LogisticRegression(random_state=42, max_iter=3000, C=0.01)\n", "model_poly3.fit(X_train_poly3_scaled, y_train)\n", "\n", "# Evaluate\n", "poly3_train_pred = model_poly3.predict(X_train_poly3_scaled)\n", "poly3_test_pred = model_poly3.predict(X_test_poly3_scaled)\n", "poly3_test_proba = model_poly3.predict_proba(X_test_poly3_scaled)[:, 1]\n", "\n", "poly3_train_acc = accuracy_score(y_train, poly3_train_pred)\n", "poly3_test_acc = accuracy_score(y_test, poly3_test_pred)\n", "poly3_auc = roc_auc_score(y_test, poly3_test_proba)\n", "\n", "print(f\"Training Accuracy: {poly3_train_acc:.4f}\")\n", "print(f\"Test Accuracy: {poly3_test_acc:.4f}\")\n", "print(f\"AUC Score: {poly3_auc:.4f}\")\n", "print(f\"Improvement: {(poly3_test_acc - baseline_test_acc)*100:.2f} percentage points\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}